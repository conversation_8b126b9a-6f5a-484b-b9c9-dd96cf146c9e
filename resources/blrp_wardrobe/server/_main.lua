-- Wardrobe Server Main
tWardrobe = T.getInstance('blrp_wardrobe', 'wardrobe')

current_version = GetConvarInt('sv_enforceGameBuild')
wardrobe_cache = {}
folder_cache = {}

-- Load wardrobe data from database
function loadWardrobe(user_id)
    local p = promise.new()
    
    user_id = tonumber(user_id)
    
    MySQL.Async.fetchAll('SELECT * FROM vrp_user_data WHERE user_id = @user_id AND dkey = "vRP:home:wardrobe"', {
        user_id = user_id
    }, function(rows)
        if #rows <= 0 then
            wardrobe_cache[user_id] = {}
            MySQL.Async.insert('INSERT INTO vrp_user_data (user_id, dkey, dvalue) VALUES (@user_id, "vRP:home:wardrobe", @dvalue)', {
                user_id = user_id,
                dvalue = json.encode({})
            })
        else
            local wardrobe_data = json.decode(rows[1].dvalue)
            
            -- Migrate old wardrobe data to include folders and timestamps
            for outfit_name, outfit_data in pairs(wardrobe_data) do
                if not outfit_data.folder then
                    outfit_data.folder = 'default'
                end
                if not outfit_data.created_at then
                    outfit_data.created_at = os.time()
                end
            end
            
            wardrobe_cache[user_id] = wardrobe_data
        end
        
        p:resolve()
    end)
    
    return Citizen.Await(p)
end

-- Save wardrobe data to database
function saveWardrobe(user_id, wardrobe_data)
    wardrobe_cache[user_id] = wardrobe_data

    MySQL.Sync.insert('INSERT INTO vrp_user_data (user_id, dkey, dvalue) VALUES (@user_id, "vRP:home:wardrobe", @dvalue) ON DUPLICATE KEY UPDATE dvalue = @dvalue', {
        user_id = user_id,
        dvalue = json.encode(wardrobe_data)
    })
end

-- Load folders from database
function loadFolders(user_id)
    local p = promise.new()

    user_id = tonumber(user_id)

    MySQL.Async.fetchAll('SELECT * FROM wardrobe_folders WHERE user_id = @user_id ORDER BY name ASC', {
        user_id = user_id
    }, function(rows)
        folder_cache[user_id] = {}
        for _, row in ipairs(rows) do
            folder_cache[user_id][row.id] = {
                id = row.id,
                name = row.name,
                color = row.color,
                created_at = row.created_at
            }
        end

        p:resolve()
    end)

    return Citizen.Await(p)
end

-- Create a new folder
function createFolder(user_id, name, color)
    local p = promise.new()

    user_id = tonumber(user_id)
    color = color or '#5865f2'

    MySQL.Async.insert('INSERT INTO wardrobe_folders (user_id, name, color) VALUES (@user_id, @name, @color)', {
        user_id = user_id,
        name = name,
        color = color
    }, function(insertId)
        if insertId then
            -- Update cache
            if not folder_cache[user_id] then
                folder_cache[user_id] = {}
            end
            folder_cache[user_id][insertId] = {
                id = insertId,
                name = name,
                color = color,
                created_at = os.time()
            }
            p:resolve(insertId)
        else
            p:resolve(false)
        end
    end)

    return Citizen.Await(p)
end

-- Delete a folder and unassign outfits
function deleteFolder(user_id, folder_id)
    local p = promise.new()

    user_id = tonumber(user_id)
    folder_id = tonumber(folder_id)

    -- First, remove folder_id from all outfits that use this folder
    if wardrobe_cache[user_id] then
        for outfit_name, outfit_data in pairs(wardrobe_cache[user_id]) do
            if outfit_data.folder_id == folder_id then
                outfit_data.folder_id = nil
            end
        end

        -- Save updated wardrobe
        saveWardrobe(user_id, wardrobe_cache[user_id])
    end

    -- Delete the folder
    MySQL.Async.execute('DELETE FROM wardrobe_folders WHERE id = @folder_id AND user_id = @user_id', {
        folder_id = folder_id,
        user_id = user_id
    }, function(affectedRows)
        if affectedRows > 0 then
            -- Update cache
            if folder_cache[user_id] then
                folder_cache[user_id][folder_id] = nil
            end
            p:resolve(true)
        else
            p:resolve(false)
        end
    end)

    return Citizen.Await(p)
end

-- Open wardrobe interface
RegisterNetEvent('blrp_wardrobe:server:openWardrobe', function(allow_dress, player, allow_undress, force_user_id)
    if allow_dress == nil then
        allow_dress = true
    end
    
    if allow_undress == nil then
        allow_undress = true
    end
    
    if not player then
        player = source
    end
    
    local character = exports.blrp_core:character(player)
    local user_id = tonumber(character.get('identifier'))
    
    if force_user_id then
        user_id = force_user_id
    end
    
    if not wardrobe_cache[user_id] then
        loadWardrobe(user_id)
    end

    if not folder_cache[user_id] then
        loadFolders(user_id)
    end

    local wardrobe = wardrobe_cache[user_id]
    local folders = folder_cache[user_id]

    if not wardrobe then
        wardrobe = {}
    end

    if not folders then
        folders = {}
    end

    local wardrobe_menu_options = {}

    if allow_dress then
        for outfit_name, outfit_data in pairs(wardrobe) do
            table.insert(wardrobe_menu_options, {
                name = outfit_name,
                version = outfit_data.version or 2372,
                favorite = outfit_data.favorite,
                save_hair = outfit_data.save_hair or false,
                folder_id = outfit_data.folder_id,
                created_at = outfit_data.created_at or os.time()
            })
        end
    end

    -- Convert folders to array format for client
    local folders_array = {}
    for folder_id, folder_data in pairs(folders) do
        table.insert(folders_array, folder_data)
    end

    -- Use new wardrobe interface instead of BMenu
    TriggerClientEvent('blrp_wardrobe:client:openWardrobe', character.source, current_version, allow_dress, allow_undress, wardrobe_menu_options, force_user_id, folders_array)
end)

-- Handle wardrobe opening from clothing store
RegisterNetEvent('blrp_clothingstore:server:openWardrobe', function(allow_dress, player, allow_undress, force_user_id)
    TriggerEvent('blrp_wardrobe:server:openWardrobe', allow_dress, player, allow_undress, force_user_id)
end)



-- Export functions for other resources
exports('OpenWardrobe', function(player, allow_dress, allow_undress, force_user_id)
    TriggerEvent('blrp_wardrobe:server:openWardrobe', allow_dress, player, allow_undress, force_user_id)
end)

exports('LoadWardrobe', function(user_id)
    return loadWardrobe(user_id)
end)

exports('SaveWardrobe', function(user_id, wardrobe_data)
    return saveWardrobe(user_id, wardrobe_data)
end)

exports('GetWardrobeCache', function()
    return wardrobe_cache
end)
