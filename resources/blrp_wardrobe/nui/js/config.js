// Configuration and global variables
const WardrobeConfig = {
    animations: {
        fadeIn: 200,
        fadeOut: 150,
        modalFade: 150
    },
    
    defaults: {
        folderColor: '#5865f2',
        sortBy: 'name',
        selectedFolder: 'all'
    },
    
    limits: {
        outfitNameLength: 25,
        folderNameLength: 50
    }
};

// Global state
let wardrobeData = {};
let nearbyPlayers = [];
let currentOutfits = [];
let currentFolders = [];
let searchQuery = '';
let sortBy = WardrobeConfig.defaults.sortBy;
let selectedFolder = WardrobeConfig.defaults.selectedFolder;
