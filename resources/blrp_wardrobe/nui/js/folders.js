// Folder management functions
const WardrobeFolders = {
    // Update folder options and tags
    updateFolderOptions() {
        const outfitFolderSelect = $('#outfit-folder-select');
        const folderTags = $('#folder-tags');
        
        // Clear and repopulate select
        outfitFolderSelect.empty();
        outfitFolderSelect.append(`<option value="">Unassigned</option>`);
        
        // Clear and repopulate folder tags
        folderTags.empty();
        
        // Add "All" tag
        const allCount = currentOutfits.length;
        folderTags.append(`
            <div class="folder-tag ${selectedFolder === 'all' ? 'active' : ''}" data-folder="all">
                <i data-lucide="grid-3x3"></i>
                All
                <span class="folder-count">${allCount}</span>
            </div>
        `);
        
        // Add "Unassigned" tag
        const unassignedCount = currentOutfits.filter(outfit => !outfit.folder_id).length;
        if (unassignedCount > 0) {
            folderTags.append(`
                <div class="folder-tag ${selectedFolder === 'unassigned' ? 'active' : ''}" data-folder="unassigned">
                    <i data-lucide="folder-x"></i>
                    Unassigned
                    <span class="folder-count">${unassignedCount}</span>
                </div>
            `);
        }
        
        // Add actual folders
        currentFolders.forEach(folder => {
            const folderCount = currentOutfits.filter(outfit => outfit.folder_id == folder.id).length;
            
            // Add to select dropdown
            outfitFolderSelect.append(`<option value="${folder.id}">${folder.name}</option>`);
            
            // Add to folder tags
            folderTags.append(`
                <div class="folder-tag ${selectedFolder == folder.id ? 'active' : ''}" 
                     data-folder="${folder.id}" 
                     data-folder-name="${folder.name}"
                     style="border-color: ${folder.color};">
                    <i data-lucide="folder"></i>
                    ${folder.name}
                    <span class="folder-count">${folderCount}</span>
                </div>
            `);
        });
        
        // Initialize Lucide icons for new tags
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
        
        this.bindFolderTagEvents();
    },

    // Bind folder tag events
    bindFolderTagEvents() {
        // Bind folder tag click events
        $('.folder-tag').off('click').on('click', function() {
            const folder = $(this).data('folder');
            selectedFolder = folder;
            
            // Update active state
            $('.folder-tag').removeClass('active');
            $(this).addClass('active');
            
            // Re-render outfits
            WardrobeOutfits.render();
        });
        
        // Removed drag and drop functionality
    },

    // Show create folder modal
    showCreateModal() {
        const modal = $('#new-folder-modal');
        if (modal.is(':visible')) return;
        
        $('#folder-name-input').val('');
        
        modal.show().css('opacity', '0').animate({opacity: 1}, WardrobeConfig.animations.modalFade);
        setTimeout(() => $('#folder-name-input').focus(), 200);
    },

    // Hide create folder modal
    hideCreateModal() {
        const modal = $('#new-folder-modal');
        modal.animate({opacity: 0}, 100, function() {
            $(this).hide();
        });
    },

    // Create folder
    create() {
        const name = $('#folder-name-input').val().trim();
        
        if (!name || name.length > WardrobeConfig.limits.folderNameLength) {
            alert(`Please enter a valid folder name (1-${WardrobeConfig.limits.folderNameLength} characters)`);
            return;
        }
        
        WardrobeAPI.createFolder(name);
        this.hideCreateModal();
    },

    // Move outfit to folder
    moveOutfitToFolder(outfitName, folderId) {
        WardrobeAPI.moveOutfitToFolder(outfitName, folderId);
    }
};
