// Folder management functions
const WardrobeFolders = {
    // Update folder options and tags
    updateFolderOptions() {
        const outfitFolderSelect = $('#outfit-folder-select');
        const folderTags = $('#folder-tags');
        
        // Clear and repopulate select
        outfitFolderSelect.empty();
        outfitFolderSelect.append(`<option value="">Unassigned</option>`);
        
        // Clear and repopulate folder tags
        folderTags.empty();
        
        // Add "All" tag
        const allCount = currentOutfits.length;
        folderTags.append(`
            <div class="folder-tag ${selectedFolder === 'all' ? 'active' : ''}" data-folder="all">
                <i data-lucide="grid-3x3"></i>
                All
                <span class="folder-count">${allCount}</span>
            </div>
        `);
        
        // Add "Unassigned" tag
        const unassignedCount = currentOutfits.filter(outfit => !outfit.folder_id).length;
        if (unassignedCount > 0) {
            folderTags.append(`
                <div class="folder-tag ${selectedFolder === 'unassigned' ? 'active' : ''}" data-folder="unassigned">
                    <i data-lucide="folder-x"></i>
                    Unassigned
                    <span class="folder-count">${unassignedCount}</span>
                </div>
            `);
        }
        
        // Add actual folders
        currentFolders.forEach(folder => {
            const folderCount = currentOutfits.filter(outfit => outfit.folder_id == folder.id).length;
            
            // Add to select dropdown
            outfitFolderSelect.append(`<option value="${folder.id}">${folder.name}</option>`);
            
            // Add to folder tags
            folderTags.append(`
                <div class="folder-tag ${selectedFolder == folder.id ? 'active' : ''}"
                     data-folder="${folder.id}"
                     data-folder-name="${folder.name}"
                     data-folder-color="${folder.color}"
                     style="border-color: ${folder.color};">
                    <i data-lucide="folder" style="color: ${folder.color};"></i>
                    ${folder.name}
                    <span class="folder-count">${folderCount}</span>
                    <button class="folder-edit-btn" data-folder-id="${folder.id}" title="Edit folder">
                        <i data-lucide="edit-2"></i>
                    </button>
                </div>
            `);
        });
        
        // Initialize Lucide icons for new tags
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
        
        this.bindFolderTagEvents();
    },

    // Bind folder tag events
    bindFolderTagEvents() {
        // Bind folder tag click events
        $('.folder-tag').off('click').on('click', function(e) {
            // Don't trigger folder selection if clicking edit button
            if ($(e.target).closest('.folder-edit-btn').length > 0) {
                return;
            }

            const folder = $(this).data('folder');
            selectedFolder = folder;

            // Update active state
            $('.folder-tag').removeClass('active');
            $(this).addClass('active');

            // Re-render outfits
            WardrobeOutfits.render();
        });

        // Bind folder edit button events
        $('.folder-edit-btn').off('click').on('click', function(e) {
            e.stopPropagation();
            const folderId = $(this).data('folder-id');
            const folderTag = $(this).closest('.folder-tag');
            const folderName = folderTag.data('folder-name');
            const folderColor = folderTag.data('folder-color');

            WardrobeFolders.showEditModal(folderId, folderName, folderColor);
        });

        // Removed drag and drop functionality
    },

    // Show create folder modal
    showCreateModal() {
        const modal = $('#new-folder-modal');
        if (modal.is(':visible')) return;

        $('#folder-name-input').val('');
        $('#folder-color-input').val(WardrobeConfig.defaults.folderColor);

        // Initialize color picker
        const colorPickerHtml = this.generateColorPicker('folder-color-input', WardrobeConfig.defaults.folderColor);
        $('#folder-color-picker').html(colorPickerHtml);

        modal.show().css('opacity', '0').animate({opacity: 1}, WardrobeConfig.animations.modalFade);
        setTimeout(() => $('#folder-name-input').focus(), 200);
    },

    // Hide create folder modal
    hideCreateModal() {
        const modal = $('#new-folder-modal');
        modal.animate({opacity: 0}, 100, function() {
            $(this).hide();
        });
    },

    // Create folder
    create() {
        const name = $('#folder-name-input').val().trim();
        const color = $('#folder-color-input').val() || WardrobeConfig.defaults.folderColor;

        if (!name || name.length > WardrobeConfig.limits.folderNameLength) {
            alert(`Please enter a valid folder name (1-${WardrobeConfig.limits.folderNameLength} characters)`);
            return;
        }

        WardrobeAPI.createFolder(name, color);
        this.hideCreateModal();
    },

    // Show edit folder modal
    showEditModal(folderId, folderName, folderColor) {
        const modal = $('#edit-folder-modal');
        if (modal.is(':visible')) return;

        $('#edit-folder-id').val(folderId);
        $('#edit-folder-name-input').val(folderName);
        $('#edit-folder-color-input').val(folderColor);

        // Initialize color picker
        const colorPickerHtml = this.generateColorPicker('edit-folder-color-input', folderColor);
        $('#edit-folder-color-picker').html(colorPickerHtml);

        modal.show().css('opacity', '0').animate({opacity: 1}, WardrobeConfig.animations.modalFade);
        setTimeout(() => $('#edit-folder-name-input').focus(), 200);
    },

    // Hide edit folder modal
    hideEditModal() {
        const modal = $('#edit-folder-modal');
        modal.animate({opacity: 0}, 100, function() {
            $(this).hide();
        });
    },

    // Update folder
    update() {
        const folderId = $('#edit-folder-id').val();
        const name = $('#edit-folder-name-input').val().trim();
        const color = $('#edit-folder-color-input').val() || WardrobeConfig.defaults.folderColor;

        if (!name || name.length > WardrobeConfig.limits.folderNameLength) {
            alert(`Please enter a valid folder name (1-${WardrobeConfig.limits.folderNameLength} characters)`);
            return;
        }

        WardrobeAPI.updateFolder(folderId, name, color);
        this.hideEditModal();
    },

    // Generate color picker HTML
    generateColorPicker(inputId, selectedColor) {
        let html = '<div class="color-picker">';

        WardrobeConfig.colors.forEach(color => {
            const isSelected = selectedColor === color.value ? 'selected' : '';
            html += `
                <div class="color-option ${isSelected}"
                     data-color="${color.value}"
                     data-input="${inputId}"
                     style="background-color: ${color.value};"
                     title="${color.name}">
                </div>
            `;
        });

        html += '</div>';
        return html;
    },

    // Move outfit to folder
    moveOutfitToFolder(outfitName, folderId) {
        WardrobeAPI.moveOutfitToFolder(outfitName, folderId);
    }
};
