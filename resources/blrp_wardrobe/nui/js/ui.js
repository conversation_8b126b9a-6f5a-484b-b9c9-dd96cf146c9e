// UI management functions
const WardrobeUI = {
    // Initialize the wardrobe interface
    initialize() {
        const html = `
            <div id="wardrobe-container" class="wardrobe-container" style="display: none;">
                <div class="wardrobe-main">
                    <!-- Header -->
                    <div class="wardrobe-header">
                        <h2>
                            <i data-lucide="shirt" class="icon"></i>
                            Personal Wardrobe
                        </h2>
                        <button id="close-wardrobe">
                            <i data-lucide="x"></i>
                        </button>
                    </div>

                    <!-- Toolbar -->
                    <div class="wardrobe-toolbar">
                        <!-- Folder Tags -->
                        <div class="folder-tags-container">
                            <div id="folder-tags" class="folder-tags">
                                <!-- Folder tags will be populated here -->
                            </div>
                            <button id="new-folder-btn" class="btn btn-secondary btn-sm">
                                <i data-lucide="folder-plus"></i>
                                New Folder
                            </button>
                        </div>

                        <div class="toolbar-grid">
                            <!-- Search Bar -->
                            <div class="form-control">
                                <input 
                                    type="text" 
                                    id="search-input" 
                                    placeholder="Search outfits..." 
                                    class="form-input"
                                >
                            </div>

                            <!-- Sort Options -->
                            <div class="form-control">
                                <select id="sort-select" class="form-select">
                                    <option value="name">Name (A-Z)</option>
                                    <option value="name_desc">Name (Z-A)</option>
                                    <option value="date_desc">Date (Newest)</option>
                                    <option value="date">Date (Oldest)</option>
                                    <option value="favorite">Favorites First</option>
                                </select>
                            </div>

                            <!-- Action Buttons -->
                            <div class="toolbar-actions">
                                <button id="save-outfit-btn" class="btn btn-primary">
                                    <i data-lucide="plus"></i>
                                    Save Current Outfit
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Outfits Display -->
                    <div class="outfit-container">
                        <div id="outfits-grid">
                            <!-- Outfits will be populated here -->
                        </div>
                        
                        <div id="no-outfits" class="empty-state" style="display: none;">
                            <i data-lucide="shirt" class="icon"></i>
                            <h3>No outfits found</h3>
                            <p>Save your first outfit to get started!</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Save Outfit Modal -->
            <div id="save-outfit-modal" class="modal-overlay" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2 id="save-modal-title">Save Current Outfit</h2>
                        <button id="cancel-save" class="modal-close-btn">
                            <i data-lucide="x"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-control mb-4">
                            <input type="text" id="outfit-name-input" placeholder="Outfit name" class="form-input" maxlength="25">
                        </div>
                        <div class="form-control mb-4">
                            <label style="color: var(--text-secondary); font-size: 0.875rem; margin-bottom: 0.5rem; display: block;">
                                Save to folder:
                            </label>
                            <select id="outfit-folder-select" class="form-select">
                                <option value="">Unassigned</option>
                            </select>
                        </div>
                        <label class="flex items-center gap-2 mb-4" style="color: var(--text-secondary); font-size: 0.875rem;">
                            <input type="checkbox" id="save-hair-checkbox">
                            Save hair with this outfit
                        </label>
                        <div id="save-hair-info" class="mb-4" style="display: none; padding: 1rem; background: var(--primary-light); border-radius: var(--radius); color: var(--primary); font-size: 0.875rem;">
                            Your current hairstyle will always be applied with this outfit.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button id="cancel-save-alt" class="btn btn-secondary">Cancel</button>
                        <button id="confirm-save" class="btn btn-primary">Save</button>
                    </div>
                </div>
            </div>

            <!-- New Folder Modal -->
            <div id="new-folder-modal" class="modal-overlay" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>Create New Folder</h2>
                        <button id="cancel-folder" class="modal-close-btn">
                            <i data-lucide="x"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-control mb-4">
                            <input type="text" id="folder-name-input" placeholder="Folder name" class="form-input" maxlength="50">
                        </div>
                        <div class="form-control mb-4">
                            <label class="form-label">Folder Color</label>
                            <input type="hidden" id="folder-color-input" value="#5865f2">
                            <div id="folder-color-picker"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button id="cancel-folder-alt" class="btn btn-secondary">Cancel</button>
                        <button id="confirm-folder" class="btn btn-primary">Create</button>
                    </div>
                </div>
            </div>

            <!-- Edit Folder Modal -->
            <div id="edit-folder-modal" class="modal-overlay" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>Edit Folder</h2>
                        <button id="cancel-edit-folder" class="modal-close-btn">
                            <i data-lucide="x"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" id="edit-folder-id">
                        <div class="form-control mb-4">
                            <input type="text" id="edit-folder-name-input" placeholder="Folder name" class="form-input" maxlength="50">
                        </div>
                        <div class="form-control mb-4">
                            <label class="form-label">Folder Color</label>
                            <input type="hidden" id="edit-folder-color-input" value="#5865f2">
                            <div id="edit-folder-color-picker"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button id="cancel-edit-folder-alt" class="btn btn-secondary">Cancel</button>
                        <button id="confirm-edit-folder" class="btn btn-primary">Update</button>
                    </div>
                </div>
            </div>

            <!-- Rename Outfit Modal -->
            <div id="rename-outfit-modal" class="modal-overlay" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>Rename Outfit</h2>
                        <button id="cancel-rename" class="modal-close-btn">
                            <i data-lucide="x"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-control mb-4">
                            <input type="text" id="rename-outfit-input" placeholder="New outfit name" class="form-input" maxlength="25">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button id="cancel-rename-alt" class="btn btn-secondary">Cancel</button>
                        <button id="confirm-rename" class="btn btn-primary">Rename</button>
                    </div>
                </div>
            </div>

            <!-- Delete Outfit Modal -->
            <div id="delete-outfit-modal" class="modal-overlay" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>Delete Outfit</h2>
                        <button id="cancel-delete" class="modal-close-btn">
                            <i data-lucide="x"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p style="color: var(--text-secondary); margin-bottom: 1rem;">
                            Are you sure you want to delete "<span id="delete-outfit-name" style="color: var(--text-primary); font-weight: 600;"></span>"?
                        </p>
                        <p style="color: var(--text-danger); font-size: 0.875rem;">
                            This action cannot be undone.
                        </p>
                    </div>
                    <div class="modal-footer">
                        <button id="cancel-delete-alt" class="btn btn-secondary">Cancel</button>
                        <button id="confirm-delete" class="btn btn-danger">Delete</button>
                    </div>
                </div>
            </div>
        `;
        
        $('body').append(html);
        
        // Initialize Lucide icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    },

    // Show wardrobe
    show(data) {
        wardrobeData = data;
        currentOutfits = data.outfits || [];
        currentFolders = data.folders || [];
        
        // Populate folder options
        WardrobeFolders.updateFolderOptions();
        
        // Render outfits
        WardrobeOutfits.render();
        
        // Show interface with proper timing
        const container = $('#wardrobe-container');
        if (container.is(':visible')) {
            // Already visible, just update content
            return;
        }
        
        container.css('display', 'flex').hide().fadeIn(WardrobeConfig.animations.fadeIn);
    },

    // Close wardrobe
    close() {
        // Close any open menus first
        $('.outfit-menu').addClass('hidden');
        
        // Hide modals
        $('.modal-overlay').fadeOut(WardrobeConfig.animations.fadeOut);
        
        // Hide main interface
        $('#wardrobe-container').fadeOut(WardrobeConfig.animations.fadeOut, function() {
            // Send close event to game after animation completes
            WardrobeAPI.closeWardrobe();
        });
    }
};
