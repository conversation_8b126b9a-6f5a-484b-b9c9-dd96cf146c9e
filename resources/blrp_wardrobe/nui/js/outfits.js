// Outfit management functions
const WardrobeOutfits = {
    // Filter and sort outfits
    getFilteredOutfits() {
        let outfits = [...currentOutfits];
        
        // Filter by search query
        if (searchQuery) {
            outfits = outfits.filter(outfit => 
                outfit.name.toLowerCase().includes(searchQuery.toLowerCase())
            );
        }
        
        // Filter by folder
        if (selectedFolder !== 'all') {
            if (selectedFolder === 'unassigned') {
                outfits = outfits.filter(outfit => !outfit.folder_id);
            } else {
                outfits = outfits.filter(outfit => outfit.folder_id == selectedFolder);
            }
        }
        
        // Sort outfits
        outfits.sort((a, b) => {
            switch (sortBy) {
                case 'name':
                    return a.name.localeCompare(b.name);
                case 'name_desc':
                    return b.name.localeCompare(a.name);
                case 'date':
                    return (a.created_at || 0) - (b.created_at || 0);
                case 'date_desc':
                    return (b.created_at || 0) - (a.created_at || 0);
                case 'favorite':
                    if (a.favorite && !b.favorite) return -1;
                    if (!a.favorite && b.favorite) return 1;
                    return a.name.localeCompare(b.name);
                default:
                    return 0;
            }
        });
        
        return outfits;
    },

    // Generate folder options for outfit folder dropdown
    generateFolderOptions(outfitName) {
        let options = '';
        currentFolders.forEach(folder => {
            options += `
                <div class="folder-option" data-outfit="${outfitName}" data-folder="${folder.id}">
                    📁 ${folder.name}
                </div>
            `;
        });
        return options;
    },

    // Render outfits
    render() {
        const grid = $('#outfits-grid');
        const noOutfits = $('#no-outfits');
        const outfits = this.getFilteredOutfits();
        
        if (outfits.length === 0) {
            grid.hide();
            noOutfits.show();
            return;
        }
        
        noOutfits.hide();
        grid.show();

        // Just list all outfits together without folder categorization
        let html = '';
        outfits.forEach(outfit => {
            html += this.renderOutfitCard(outfit);
        });
        
        grid.html(html);
        
        // Initialize Lucide icons for new content
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
        
        this.bindOutfitEvents();
    },

    // Render individual outfit card
    renderOutfitCard(outfit) {
        const isOutdated = (outfit.version || 0) < wardrobeData.current_version;
        const isFavorite = outfit.favorite || false;
        
        return `
            <div class="outfit-card ${isFavorite ? 'favorite' : ''} ${isOutdated ? 'outdated' : ''}"
                 data-outfit-name="${outfit.name}">
                <div class="outfit-name">${outfit.name}</div>
                <div class="outfit-actions">
                    <button class="btn btn-primary btn-sm apply-outfit" data-outfit="${outfit.name}" ${isOutdated ? 'disabled' : ''} title="Apply outfit">
                        ✓
                    </button>
                    <button class="btn ${isFavorite ? 'btn-warning' : 'btn-secondary'} btn-sm toggle-favorite" data-outfit="${outfit.name}" title="${isFavorite ? 'Remove from favorites' : 'Add to favorites'}">
                        ${isFavorite ? '⭐' : '☆'}
                    </button>
                    <div class="outfit-folder-container">
                        <button class="outfit-folder-btn" data-outfit="${outfit.name}" title="Move to folder">
                            <i data-lucide="folder"></i>
                        </button>
                        <div class="outfit-folder-menu hidden">
                            <div class="folder-option" data-outfit="${outfit.name}" data-folder="">
                                📂 Unassigned
                            </div>
                            ${this.generateFolderOptions(outfit.name)}
                        </div>
                    </div>
                    <div class="outfit-menu-container">
                        <button class="outfit-menu-btn" data-outfit="${outfit.name}" title="More actions">
                            ⋮
                        </button>
                        <div class="outfit-menu hidden">
                            <button class="rename-outfit" data-outfit="${outfit.name}">
                                ✏️ Rename
                            </button>
                            <button class="share-outfit" data-outfit="${outfit.name}" ${nearbyPlayers.length === 0 ? 'disabled' : ''}>
                                📤 Share
                            </button>
                            ${isOutdated ? `
                            <button class="upgrade-outfit text-warning" data-outfit="${outfit.name}">
                                ⬆️ Upgrade
                            </button>
                            <hr>
                            ` : ''}
                            <button class="delete-outfit text-danger" data-outfit="${outfit.name}">
                                🗑️ Delete
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    // Bind outfit events
    bindOutfitEvents() {
        // Apply outfit
        $('.apply-outfit').off('click').on('click', function() {
            const outfitName = $(this).data('outfit');
            WardrobeAPI.applyOutfit(outfitName);
        });
        
        // Toggle favorite
        $('.toggle-favorite').off('click').on('click', function() {
            const outfitName = $(this).data('outfit');
            WardrobeAPI.toggleFavorite(outfitName);
        });
        
        // Rename outfit
        $('.rename-outfit').off('click').on('click', function() {
            const outfitName = $(this).data('outfit');
            WardrobeOutfits.showRenameModal(outfitName);
        });
        
        // Share outfit
        $('.share-outfit').off('click').on('click', function() {
            const outfitName = $(this).data('outfit');
            WardrobeOutfits.showShareMenu(outfitName);
        });
        
        // Upgrade outfit
        $('.upgrade-outfit').off('click').on('click', function() {
            const outfitName = $(this).data('outfit');
            // Use confirm dialog for gender selection as a simple solution
            const isMale = confirm(`Upgrade "${outfitName}"?\n\nClick OK for Male, Cancel for Female`);
            const gender = isMale ? 'male' : 'female';
            WardrobeAPI.upgradeOutfit(outfitName, gender);
        });
        
        // Delete outfit
        $('.delete-outfit').off('click').on('click', function() {
            const outfitName = $(this).data('outfit');
            WardrobeOutfits.showDeleteModal(outfitName);
        });
        
        this.bindMenuEvents();
        this.bindFolderEvents();
    },

    // Bind menu events
    bindMenuEvents() {
        // Clear any existing timers
        if (window.menuCloseTimer) {
            clearTimeout(window.menuCloseTimer);
            window.menuCloseTimer = null;
        }

        // Bind menu toggle events with proper positioning
        $('.outfit-menu-btn').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const container = $(this).closest('.outfit-menu-container');
            const menu = container.find('.outfit-menu');
            const isCurrentlyHidden = menu.hasClass('hidden');

            // Clear any pending close timer
            if (window.menuCloseTimer) {
                clearTimeout(window.menuCloseTimer);
                window.menuCloseTimer = null;
            }

            // Close all other menus first
            $('.outfit-menu').addClass('hidden');

            // Toggle current menu
            if (isCurrentlyHidden) {
                menu.removeClass('hidden');

                // Adjust position if menu goes outside viewport
                setTimeout(() => {
                    const menuRect = menu[0].getBoundingClientRect();
                    const viewportWidth = window.innerWidth;
                    const viewportHeight = window.innerHeight;

                    // If menu goes outside right edge, position it to the left
                    if (menuRect.right > viewportWidth - 20) {
                        menu.css({
                            'right': '0',
                            'left': 'auto'
                        });
                    }

                    // If menu goes outside bottom edge, position it above
                    if (menuRect.bottom > viewportHeight - 20) {
                        menu.css({
                            'top': 'auto',
                            'bottom': '100%',
                            'margin-top': '0',
                            'margin-bottom': '0.5rem'
                        });
                    }
                }, 10);
            }
        });

        // Handle menu container hover to keep menu open
        $('.outfit-menu-container, .outfit-folder-container').off('mouseenter.outfit-menu mouseleave.outfit-menu')
            .on('mouseenter.outfit-menu', function(e) {
                // Clear any pending close timer when hovering over container
                if (window.menuCloseTimer) {
                    clearTimeout(window.menuCloseTimer);
                    window.menuCloseTimer = null;
                }
            })
            .on('mouseleave.outfit-menu', function(e) {
                // Set a timer to close the menu after leaving the container
                const menu = $(this).find('.outfit-menu, .outfit-folder-menu');
                if (!menu.hasClass('hidden')) {
                    window.menuCloseTimer = setTimeout(() => {
                        menu.addClass('hidden');
                        window.menuCloseTimer = null;
                    }, 300); // 300ms delay before closing
                }
            });

        // Handle menu hover to keep it open
        $('.outfit-menu, .outfit-folder-menu').off('mouseenter.outfit-menu mouseleave.outfit-menu')
            .on('mouseenter.outfit-menu', function(e) {
                // Clear any pending close timer when hovering over menu
                if (window.menuCloseTimer) {
                    clearTimeout(window.menuCloseTimer);
                    window.menuCloseTimer = null;
                }
            })
            .on('mouseleave.outfit-menu', function(e) {
                // Set a timer to close the menu after leaving
                const menu = $(this);
                window.menuCloseTimer = setTimeout(() => {
                    menu.addClass('hidden');
                    window.menuCloseTimer = null;
                }, 300); // 300ms delay before closing
            });

        // Close menus when clicking outside
        $(document).off('click.outfit-menu').on('click.outfit-menu', function(e) {
            if (!$(e.target).closest('.outfit-menu-container, .outfit-folder-container').length) {
                $('.outfit-menu, .outfit-folder-menu').addClass('hidden');
                if (window.menuCloseTimer) {
                    clearTimeout(window.menuCloseTimer);
                    window.menuCloseTimer = null;
                }
            }
        });

        // Folder options are handled in bindFolderEvents

        // Prevent menu buttons from closing the menu
        $('.outfit-menu button').off('click.outfit-menu').on('click.outfit-menu', function(e) {
            // Close menu after action
            $(this).closest('.outfit-menu').addClass('hidden');
            if (window.menuCloseTimer) {
                clearTimeout(window.menuCloseTimer);
                window.menuCloseTimer = null;
            }
        });
    },

    // Bind folder events
    bindFolderEvents() {
        // Bind folder dropdown toggle events
        $('.outfit-folder-btn').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const container = $(this).closest('.outfit-folder-container');
            const menu = container.find('.outfit-folder-menu');
            const isCurrentlyHidden = menu.hasClass('hidden');

            // Close all other folder menus
            $('.outfit-folder-menu').addClass('hidden');

            // Toggle current menu
            if (isCurrentlyHidden) {
                menu.removeClass('hidden');
            }
        });

        // Bind folder option click events
        $('.folder-option').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const outfitName = $(this).data('outfit');
            const folderId = $(this).data('folder');

            // Move outfit to folder
            WardrobeAPI.moveOutfitToFolder(outfitName, folderId || null);

            // Close menu
            $(this).closest('.outfit-folder-menu').addClass('hidden');
        });

        // Close folder menus when clicking outside
        $(document).off('click.outfit-folder').on('click.outfit-folder', function(e) {
            if (!$(e.target).closest('.outfit-folder-container').length) {
                $('.outfit-folder-menu').addClass('hidden');
            }
        });
    },

    // Show share menu
    showShareMenu(outfitName) {
        if (nearbyPlayers.length === 0) {
            alert('No nearby players found');
            return;
        }

        // Create a simple selection dialog using confirm dialogs
        let message = `Share "${outfitName}" with:\n\n`;
        nearbyPlayers.forEach((player, index) => {
            message += `${index + 1}. ${player.name}\n`;
        });

        // For now, just share with the first player as a simple implementation
        // TODO: Create a proper player selection modal
        if (confirm(message + '\nShare with the first player?')) {
            const targetPlayer = nearbyPlayers[0];
            WardrobeAPI.shareOutfit(outfitName, targetPlayer.id);
        }
    },

    // Show save outfit modal
    showSaveModal() {
        const modal = $('#save-outfit-modal');
        if (modal.is(':visible')) return;

        // Clear form
        $('#outfit-name-input').val('');
        $('#save-hair-checkbox').prop('checked', false);
        $('#save-hair-info').hide();

        // Auto-populate folder based on current selection
        const folderSelect = $('#outfit-folder-select');
        const modalTitle = $('#save-modal-title');

        if (selectedFolder && selectedFolder !== 'all' && selectedFolder !== 'unassigned') {
            // If a specific folder is selected, pre-select it
            folderSelect.val(selectedFolder);

            // Find folder name for title
            const folder = currentFolders.find(f => f.id == selectedFolder);
            const folderName = folder ? folder.name : 'Selected Folder';
            modalTitle.text(`Save to "${folderName}"`);
        } else {
            // Default to unassigned
            folderSelect.val('');
            modalTitle.text('Save Current Outfit');
        }

        modal.show().css('opacity', '0').animate({opacity: 1}, WardrobeConfig.animations.modalFade);
        setTimeout(() => $('#outfit-name-input').focus(), 200);
    },

    // Hide save outfit modal
    hideSaveModal() {
        const modal = $('#save-outfit-modal');
        modal.animate({opacity: 0}, 100, function() {
            $(this).hide();
        });
    },

    // Save outfit
    save() {
        const name = $('#outfit-name-input').val().trim();
        const folderId = $('#outfit-folder-select').val();
        const saveHair = $('#save-hair-checkbox').is(':checked');

        if (!name || name.length > WardrobeConfig.limits.outfitNameLength) {
            alert(`Please enter a valid outfit name (1-${WardrobeConfig.limits.outfitNameLength} characters)`);
            return;
        }

        WardrobeAPI.saveOutfit(name, folderId, saveHair);
        this.hideSaveModal();
    },

    // Show rename outfit modal
    showRenameModal(outfitName) {
        const modal = $('#rename-outfit-modal');
        if (modal.is(':visible')) return;

        // Store the outfit name for later use
        modal.data('outfit-name', outfitName);

        // Pre-fill with current name
        $('#rename-outfit-input').val(outfitName);

        modal.show().css('opacity', '0').animate({opacity: 1}, WardrobeConfig.animations.modalFade);
        setTimeout(() => {
            $('#rename-outfit-input').focus().select();
        }, 200);
    },

    // Hide rename outfit modal
    hideRenameModal() {
        const modal = $('#rename-outfit-modal');
        modal.animate({opacity: 0}, 100, function() {
            $(this).hide().removeData('outfit-name');
        });
    },

    // Rename outfit
    rename() {
        const modal = $('#rename-outfit-modal');
        const oldName = modal.data('outfit-name');
        const newName = $('#rename-outfit-input').val().trim();

        if (!newName || newName.length > WardrobeConfig.limits.outfitNameLength) {
            alert(`Please enter a valid outfit name (1-${WardrobeConfig.limits.outfitNameLength} characters)`);
            return;
        }

        if (newName === oldName) {
            this.hideRenameModal();
            return;
        }

        WardrobeAPI.renameOutfit(oldName, newName);
        this.hideRenameModal();
    },

    // Show delete outfit modal
    showDeleteModal(outfitName) {
        const modal = $('#delete-outfit-modal');
        if (modal.is(':visible')) return;

        // Store the outfit name for later use
        modal.data('outfit-name', outfitName);

        // Update the outfit name in the modal
        $('#delete-outfit-name').text(outfitName);

        modal.show().css('opacity', '0').animate({opacity: 1}, WardrobeConfig.animations.modalFade);
    },

    // Hide delete outfit modal
    hideDeleteModal() {
        const modal = $('#delete-outfit-modal');
        modal.animate({opacity: 0}, 100, function() {
            $(this).hide().removeData('outfit-name');
        });
    },

    // Delete outfit
    deleteOutfit() {
        const modal = $('#delete-outfit-modal');
        const outfitName = modal.data('outfit-name');

        if (!outfitName) {
            this.hideDeleteModal();
            return;
        }

        WardrobeAPI.deleteOutfit(outfitName);
        this.hideDeleteModal();
    }
};
