/* Modern Sleek Wardrobe Interface */

:root {
    --primary: #5865f2;
    --primary-hover: #4752c4;
    --primary-light: rgba(88, 101, 242, 0.1);
    --secondary: #4f545c;
    --success: #3ba55d;
    --warning: #faa61a;
    --danger: #ed4245;
    --bg-primary: #36393f;
    --bg-secondary: #2f3136;
    --bg-tertiary: #292b2f;
    --bg-quaternary: #202225;
    --text-primary: #ffffff;
    --text-secondary: #b9bbbe;
    --text-muted: #72767d;
    --border: #40444b;
    --border-light: #4f545c;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.2);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.3), 0 1px 2px -1px rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);
    --radius: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    margin: 0;
    padding: 0;
    background: transparent;
    overflow: hidden;
    font-size: 14px;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container */
.wardrobe-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: fadeIn 0.2s ease-out;
}

.wardrobe-main {
    width: min(95vw, 1100px);
    height: min(90vh, 750px);
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    animation: slideUp 0.3s cubic-bezier(0.16, 1, 0.3, 1);
    display: flex;
    flex-direction: column;
}

/* Header */
.wardrobe-header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border);
    padding: 1.5rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
}

.wardrobe-header h2 {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.wardrobe-header .icon {
    color: var(--primary);
    width: 24px;
    height: 24px;
}

#close-wardrobe {
    background: var(--bg-secondary);
    border: none;
    color: var(--text-secondary);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    cursor: pointer;
}

#close-wardrobe:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    transform: scale(1.05);
}

/* Toolbar */
.wardrobe-toolbar {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border);
    padding: 1.5rem 2rem;
    flex-shrink: 0;
}

.toolbar-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.toolbar-actions {
    display: flex;
    gap: 0.75rem;
}

/* Folder Tags */
.folder-tags-container {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border);
}

.folder-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    flex: 1;
}

.folder-tag {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border);
    border-radius: 20px;
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease;
    user-select: none;
    position: relative;
}

.folder-tag:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-light);
    color: var(--text-primary);
}

.folder-tag.active {
    background: var(--primary);
    border-color: var(--primary);
    color: white;
}

.folder-tag.active:hover {
    background: var(--primary-hover);
}

.folder-tag .folder-count {
    background: rgba(255, 255, 255, 0.2);
    color: inherit;
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 0.25rem;
}

.folder-tag.active .folder-count {
    background: rgba(255, 255, 255, 0.3);
}

/* Removed drag and drop styles */

/* Form Controls */
.form-control {
    position: relative;
}

.form-input, .form-select {
    width: 100%;
    padding: 0.75rem 1rem;
    background: var(--bg-primary);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 400;
    transition: all 0.2s ease;
}

.form-input:focus, .form-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-light);
}

.form-input::placeholder {
    color: var(--text-muted);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: var(--radius);
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border);
}

.btn-secondary:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-light);
}

.btn-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    min-width: 40px;
    height: 36px;
}

.outfit-actions .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    min-width: 36px;
    height: 32px;
}

/* Content Area */
.outfit-container {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
    background: var(--bg-tertiary);
}

/* Folder Section */
.folder-section {
    margin-bottom: 2rem;
}

.folder-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding: 1rem 1.5rem;
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border);
}

.folder-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1rem;
    flex: 1;
}

.folder-count {
    background: var(--primary-light);
    color: var(--primary);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.folder-edit-btn {
    background: none;
    border: none;
    color: var(--text-muted);
    padding: 0.25rem;
    border-radius: 4px;
    cursor: pointer;
    opacity: 0;
    transition: all 0.2s ease;
    margin-left: auto;
}

.folder-tag:hover .folder-edit-btn {
    opacity: 1;
}

.folder-edit-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.folder-edit-btn i {
    width: 14px;
    height: 14px;
}

#outfits-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

#outfits-grid .outfit-card {
    width: auto;
}

/* Outfit Cards */
.outfit-card {
    background: var(--bg-primary);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    padding: 1rem 1.5rem;
    transition: all 0.2s ease;
    position: relative;
    overflow: visible;
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 60px;
}

.outfit-card:hover {
    border-color: var(--primary);
    box-shadow: var(--shadow-md);
}

.outfit-card.favorite {
    border-color: var(--warning);
    background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(245, 158, 11, 0.05) 100%);
}

.outfit-card.outdated {
    border-color: var(--warning);
    background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(245, 158, 11, 0.05) 100%);
}

.outfit-name {
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
    margin-right: 1rem;
}

.outfit-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-shrink: 0;
}

.outfit-menu-container, .outfit-folder-container {
    position: relative;
}

.outfit-menu-btn, .outfit-folder-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border);
    color: var(--text-muted);
    width: 32px;
    height: 32px;
    border-radius: var(--radius);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.15s ease;
    font-size: 14px;
}

.outfit-menu-btn:hover, .outfit-folder-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-light);
}

/* Dropdown Menus */
.outfit-menu, .outfit-folder-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 0.5rem;
    background: var(--bg-quaternary);
    border: 1px solid var(--border);
    border-radius: var(--radius);
    box-shadow: var(--shadow-xl);
    z-index: 1000;
    min-width: 200px;
    overflow: hidden;
    animation: slideDown 0.1s ease-out;
    user-select: none;
}

.outfit-menu button, .folder-option {
    width: 100%;
    text-align: left;
    padding: 0.75rem 1rem;
    color: var(--text-secondary);
    background: transparent;
    border: none;
    cursor: pointer;
    transition: all 0.1s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.875rem;
    font-weight: 400;
    white-space: nowrap;
}

.outfit-menu button:hover, .folder-option:hover {
    background: var(--primary);
    color: var(--text-primary);
}

.outfit-menu button:hover .icon {
    color: var(--text-primary);
}

.outfit-menu button:disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

.outfit-menu button:disabled:hover {
    background: transparent;
    color: var(--text-secondary);
}

.outfit-menu button:disabled:hover .icon {
    color: var(--text-muted);
}

.outfit-menu hr {
    border: none;
    border-top: 1px solid var(--border);
    margin: 0.25rem 0;
}

.outfit-menu .icon {
    width: 16px;
    height: 16px;
    color: var(--text-muted);
    flex-shrink: 0;
}

.outfit-menu .text-danger {
    color: var(--danger);
}

.outfit-menu .text-danger .icon {
    color: var(--danger);
}

.outfit-menu .text-danger:hover {
    background: var(--danger);
    color: var(--text-primary);
}

.outfit-menu .text-danger:hover .icon {
    color: var(--text-primary);
}

.outfit-menu .text-warning {
    color: var(--warning);
}

.outfit-menu .text-warning .icon {
    color: var(--warning);
}

.outfit-menu .text-warning:hover {
    background: var(--warning);
    color: var(--bg-primary);
}

.outfit-menu .text-warning:hover .icon {
    color: var(--bg-primary);
}

/* Removed favorite badge - using inline button instead */

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-muted);
}

.empty-state .icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 1.5rem;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.empty-state p {
    font-size: 0.875rem;
    margin: 0;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-8px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Scrollbar */
.outfit-container::-webkit-scrollbar {
    width: 6px;
}

.outfit-container::-webkit-scrollbar-track {
    background: transparent;
}

.outfit-container::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 3px;
}

.outfit-container::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* Color Picker */
.color-picker {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.color-option {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s ease;
    position: relative;
}

.color-option:hover {
    transform: scale(1.1);
    border-color: var(--text-secondary);
}

.color-option.selected {
    border-color: var(--text-primary);
    transform: scale(1.1);
}

.color-option.selected::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    font-size: 14px;
    text-shadow: 0 0 3px rgba(0, 0, 0, 0.8);
}

.form-label {
    display: block;
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

/* Utility Classes */
.hidden { display: none !important; }
.flex { display: flex; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.text-warning { color: var(--warning); }
.text-danger { color: var(--danger); }

/* Modal styles - Separate from wardrobe container */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.75);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 20000;
    opacity: 0;
}

.modal-content {
    background: var(--bg-primary);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    max-width: 450px;
    width: 90%;
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    transform: translateY(0);
}

.modal-header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border);
    padding: 1.5rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h2 {
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 700;
    margin: 0;
}

.modal-close-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.15s ease;
}

.modal-close-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border);
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

/* Modal animations - removed to prevent conflicts */
